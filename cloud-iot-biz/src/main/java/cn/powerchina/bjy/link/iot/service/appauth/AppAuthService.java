package cn.powerchina.bjy.link.iot.service.appauth;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.appauth.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.appauth.AppAuthDO;
import jakarta.validation.*;

/**
 * 应用管理认证 Service 接口
 *
 * <AUTHOR>
 */
public interface AppAuthService {

    /**
     * 创建应用管理认证
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAppAuth(@Valid AppAuthCreateReqVO createReqVO);

    /**
     * 更新应用管理认证
     *
     * @param updateReqVO 更新信息
     */
    void updateAppAuth(@Valid AppAuthUpdateReqVO updateReqVO);

    /**
     * 删除应用管理认证
     *
     * @param id 编号
     */
    void deleteAppAuth(Long id);

    /**
     * 获得应用管理认证
     *
     * @param id 编号
     * @return 应用管理认证
     */
    AppAuthDO getAppAuth(Long id);

    /**
     * 获得应用管理认证分页
     *
     * @param pageReqVO 分页查询
     * @return 应用管理认证分页
     */
    PageResult<AppAuthDO> getAppAuthPage(AppAuthPageReqVO pageReqVO);

    /**
     * 切换应用管理认证的启用状态
     *
     * @param appAuthSwitchReqVO
     */
    void switchStatus(AppAuthSwitchReqVO appAuthSwitchReqVO);

}